#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复测试脚本
测试修复后的增强日志管理器是否正常工作
"""

import sys
import os
import time
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'qmt_ths'))

def test_enhanced_log_manager():
    """测试增强日志管理器基本功能"""
    print("🧪 测试增强日志管理器基本功能")
    print("=" * 50)
    
    try:
        from qmt_ths.enhanced_log_manager import EnhancedLogManager, LogDisplayFormatter
        print("✅ 导入成功")
        
        # 创建实例
        log_manager = EnhancedLogManager()
        print("✅ 实例创建成功")
        
        # 测试基本日志
        log_manager.log_message("测试普通日志")
        print("✅ 普通日志测试成功")
        
        # 测试股票日志
        log_manager.log_message("002901(大博医疗): 49.15元涨停封板 无卖盘 委托价49.15")
        print("✅ 股票日志测试成功")
        
        # 测试重复股票（应该被过滤）
        log_manager.log_message("002901(大博医疗): 49.15元涨停封板 无卖盘 委托价49.15")
        print("✅ 重复股票过滤测试成功")
        
        # 获取统计信息
        stats = log_manager.get_stats()
        print(f"✅ 统计信息: {stats}")
        
        # 测试日志获取
        simplified = log_manager.get_simplified_logs()
        detailed = log_manager.get_detailed_logs()
        print(f"✅ 简化日志数量: {len(simplified)}")
        print(f"✅ 详细日志数量: {len(detailed)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_safety():
    """测试回调函数安全性"""
    print(f"\n🧪 测试回调函数安全性")
    print("=" * 50)
    
    try:
        from qmt_ths.enhanced_log_manager import EnhancedLogManager
        
        log_manager = EnhancedLogManager()
        
        # 模拟可能导致递归的回调函数
        def problematic_callback(log_entry):
            # 这种回调可能导致无限递归
            print(f"回调收到: {log_entry['message']}")
            # 不再调用log_message，避免递归
        
        # 添加回调
        log_manager.add_frontend_callback(problematic_callback)
        log_manager.add_debug_callback(problematic_callback)
        
        # 测试多个日志
        for i in range(5):
            log_manager.log_message(f"测试日志 {i}")
            time.sleep(0.1)
        
        print("✅ 回调函数安全性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 回调安全性测试失败: {str(e)}")
        return False

def test_threading_safety():
    """测试线程安全性"""
    print(f"\n🧪 测试线程安全性")
    print("=" * 50)
    
    try:
        from qmt_ths.enhanced_log_manager import EnhancedLogManager
        
        log_manager = EnhancedLogManager()
        
        def worker(thread_id):
            for i in range(10):
                log_manager.log_message(f"线程{thread_id} 日志{i}")
                time.sleep(0.01)
        
        # 创建多个线程
        threads = []
        for i in range(3):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        stats = log_manager.get_stats()
        print(f"✅ 线程安全测试完成，详细日志数量: {stats['detailed_count']}")
        return True
        
    except Exception as e:
        print(f"❌ 线程安全测试失败: {str(e)}")
        return False

def test_main_program_import():
    """测试主程序导入"""
    print(f"\n🧪 测试主程序导入")
    print("=" * 50)
    
    try:
        # 测试导入主程序的关键组件
        sys.path.append('./qmt_ths')
        
        # 测试增强日志管理器导入
        from enhanced_log_manager import EnhancedLogManager, LogDisplayFormatter
        print("✅ 增强日志管理器导入成功")
        
        # 测试主程序类导入（不实例化，只测试导入）
        import importlib.util
        spec = importlib.util.spec_from_file_location("tonghuashun_gui", "./qmt_ths/tonghuashun_gui.py")
        if spec and spec.loader:
            print("✅ 主程序文件可以加载")
        else:
            print("❌ 主程序文件加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚨 紧急修复测试")
    print("=" * 60)
    print("测试目标：验证修复后的增强日志管理器是否解决了死锁和无响应问题")
    print("=" * 60)
    
    tests = [
        ("增强日志管理器基本功能", test_enhanced_log_manager),
        ("回调函数安全性", test_callback_safety),
        ("线程安全性", test_threading_safety),
        ("主程序导入", test_main_program_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！修复应该已经解决了死锁和无响应问题。")
        print("\n💡 建议：")
        print("1. 现在可以尝试启动主程序")
        print("2. 测试开始/停止交易功能")
        print("3. 观察日志显示是否正常")
    else:
        print("⚠️ 部分测试失败，可能仍存在问题。")
        print("\n💡 建议：")
        print("1. 检查失败的测试项")
        print("2. 考虑回退到原始日志系统")
        print("3. 逐步调试问题")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
