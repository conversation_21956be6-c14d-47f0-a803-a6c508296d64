#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强日志系统功能
验证前台简化日志显示和后台详细日志功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
import time
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_log_manager import EnhancedLogManager, LogDisplayFormatter


def test_log_manager():
    """测试日志管理器功能"""
    print("🧪 测试增强日志管理器")
    print("=" * 50)
    
    # 创建日志管理器
    log_manager = EnhancedLogManager()
    
    # 测试日志
    test_logs = [
        "开始获取 002901.SZ 行情数据",
        "002901.SZ 详细行情获取调试:",
        "   准备尝试的代码格式: ['002901.SZ']",
        "   尝试获取代码: 002901.SZ",
        "002901(大博医疗): 49.15元涨停封板 无卖盘 委托价49.15",
        "开始获取 000001.SZ 行情数据",
        "000001(平安银行): 12.34元 有卖盘 委托价12.36",
        "002901(大博医疗): 49.15元涨停封板 无卖盘 委托价49.15",  # 重复，应该被过滤
        "开始获取 600036.SH 行情数据",
        "600036(招商银行): 45.67元跌停封板 无买盘 委托价45.67"
    ]
    
    # 添加测试日志
    for log in test_logs:
        log_manager.log_message(log)
        time.sleep(0.1)
    
    # 获取结果
    simplified_logs = log_manager.get_simplified_logs()
    detailed_logs = log_manager.get_detailed_logs()
    stats = log_manager.get_stats()
    
    print(f"\n📊 测试结果:")
    print(f"详细日志数量: {len(detailed_logs)}")
    print(f"简化日志数量: {len(simplified_logs)}")
    print(f"已显示股票: {stats['displayed_stocks']}")
    
    print(f"\n📋 简化日志内容:")
    for log in simplified_logs:
        formatted = LogDisplayFormatter.format_for_frontend(log)
        print(f"  {formatted}")
    
    print(f"\n📋 详细日志内容:")
    for log in detailed_logs[-5:]:  # 只显示最后5条
        formatted = LogDisplayFormatter.format_for_debug(log)
        print(f"  {formatted}")
    
    return log_manager


def create_test_gui():
    """创建测试GUI界面"""
    print(f"\n🎨 创建测试GUI界面")
    print("=" * 50)
    
    root = tk.Tk()
    root.title("增强日志系统测试")
    root.geometry("1000x700")
    
    # 创建日志管理器
    log_manager = EnhancedLogManager()
    
    # 主框架
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="增强日志系统测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 测试按钮区域
    button_frame = ttk.LabelFrame(main_frame, text="测试功能", padding=10)
    button_frame.pack(fill=tk.X, pady=(0, 10))
    
    # 日志显示区域
    log_frame = ttk.LabelFrame(main_frame, text="日志显示", padding=5)
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    # 创建Notebook用于分页显示
    log_notebook = ttk.Notebook(log_frame)
    log_notebook.pack(fill=tk.BOTH, expand=True)
    
    # 简化日志页面
    simplified_frame = ttk.Frame(log_notebook)
    log_notebook.add(simplified_frame, text="交易日志（前台）")
    
    simplified_log_text = tk.Text(simplified_frame, height=15, width=100)
    simplified_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    simplified_scrollbar = ttk.Scrollbar(simplified_frame, orient=tk.VERTICAL, command=simplified_log_text.yview)
    simplified_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    simplified_log_text['yscrollcommand'] = simplified_scrollbar.set
    
    # 详细日志页面
    detailed_frame = ttk.Frame(log_notebook)
    log_notebook.add(detailed_frame, text="详细日志（调试）")
    
    detailed_log_text = tk.Text(detailed_frame, height=15, width=100)
    detailed_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    detailed_scrollbar = ttk.Scrollbar(detailed_frame, orient=tk.VERTICAL, command=detailed_log_text.yview)
    detailed_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    detailed_log_text['yscrollcommand'] = detailed_scrollbar.set
    
    # 日志回调函数
    def update_simplified_log(log_entry):
        formatted_message = LogDisplayFormatter.format_for_frontend(log_entry)
        simplified_log_text.insert(tk.END, f"{formatted_message}\n")
        simplified_log_text.see(tk.END)
    
    def update_detailed_log(log_entry):
        formatted_message = LogDisplayFormatter.format_for_debug(log_entry)
        detailed_log_text.insert(tk.END, f"{formatted_message}\n")
        detailed_log_text.see(tk.END)
    
    # 注册回调函数
    log_manager.add_frontend_callback(update_simplified_log)
    log_manager.add_debug_callback(update_detailed_log)
    
    # 测试数据
    test_stocks = [
        ("002901", "大博医疗", "49.15元涨停封板 无卖盘 委托价49.15"),
        ("000001", "平安银行", "12.34元 有卖盘 委托价12.36"),
        ("600036", "招商银行", "45.67元跌停封板 无买盘 委托价45.67"),
        ("000002", "万科A", "15.50元 有卖盘 委托价15.58"),
        ("600000", "浦发银行", "13.13元接近涨停 有卖盘 委托价13.20")
    ]
    
    # 测试函数
    def test_stock_logs():
        """测试股票日志"""
        for code, name, status in test_stocks:
            # 模拟详细日志
            log_manager.log_message(f"开始获取 {code}.SZ 行情数据")
            log_manager.log_message(f"{code}.SZ 详细行情获取调试:")
            log_manager.log_message(f"   准备尝试的代码格式: ['{code}.SZ']")
            log_manager.log_message(f"   尝试获取代码: {code}.SZ")
            
            # 股票结果日志
            log_manager.log_message(f"{code}({name}): {status}")
            
            time.sleep(0.5)
    
    def test_duplicate_stocks():
        """测试重复股票过滤"""
        for code, name, status in test_stocks[:3]:
            log_manager.log_message(f"{code}({name}): {status}")
            time.sleep(0.2)
    
    def test_debug_logs():
        """测试调试日志"""
        debug_messages = [
            "系统启动中...",
            "✅ 连接交易服务器成功",
            "📊 开始监控板块: 医疗器械",
            "⚠️ 网络延迟较高: 500ms",
            "❌ 获取行情失败，重试中..."
        ]
        
        for msg in debug_messages:
            log_manager.log_message(msg)
            time.sleep(0.3)
    
    def clear_simplified():
        simplified_log_text.delete(1.0, tk.END)
        log_manager.clear_simplified_logs()
    
    def clear_detailed():
        detailed_log_text.delete(1.0, tk.END)
        log_manager.clear_detailed_logs()
    
    def reset_stocks():
        log_manager.clear_displayed_stocks()
        simplified_log_text.insert(tk.END, "[系统] 股票显示状态已重置\n")
    
    def show_stats():
        stats = log_manager.get_stats()
        stats_window = tk.Toplevel(root)
        stats_window.title("日志统计")
        stats_window.geometry("400x300")
        
        stats_text = tk.Text(stats_window, wrap=tk.WORD)
        stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        stats_content = f"""日志统计信息:

详细日志数量: {stats['detailed_count']}
交易日志数量: {stats['simplified_count']}
已显示股票数量: {stats['displayed_stocks_count']}

已显示的股票代码:
{', '.join(stats['displayed_stocks']) if stats['displayed_stocks'] else '暂无'}

功能说明:
• 交易日志：只显示股票结果，每只股票只显示一次
• 详细日志：显示所有日志，包括调试信息
• 重复股票会被自动过滤，不在交易日志中重复显示
• 可以通过"重置股票显示"来重新显示相同股票
"""
        
        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)
    
    # 测试按钮
    ttk.Button(button_frame, text="测试股票日志", command=lambda: threading.Thread(target=test_stock_logs, daemon=True).start()).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="测试重复过滤", command=lambda: threading.Thread(target=test_duplicate_stocks, daemon=True).start()).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="测试调试日志", command=lambda: threading.Thread(target=test_debug_logs, daemon=True).start()).pack(side=tk.LEFT, padx=(0, 5))
    
    # 管理按钮
    ttk.Separator(button_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=10, fill=tk.Y)
    ttk.Button(button_frame, text="清空交易日志", command=clear_simplified).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="清空详细日志", command=clear_detailed).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="重置股票显示", command=reset_stocks).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="显示统计", command=show_stats).pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="功能说明", padding=10)
    info_frame.pack(fill=tk.X, pady=(10, 0))
    
    info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
    info_text.pack(fill=tk.X)
    info_text.insert(tk.END, """
功能演示：
1. "测试股票日志" - 模拟完整的股票行情获取过程，包括详细调试信息和最终结果
2. "测试重复过滤" - 验证相同股票不会在交易日志中重复显示
3. "测试调试日志" - 添加各种系统调试信息
4. 交易日志页面只显示股票结果，详细日志页面显示所有信息
    """)
    info_text.config(state=tk.DISABLED)
    
    return root


def main():
    """主测试函数"""
    print("🧪 增强日志系统功能测试")
    print("=" * 60)
    
    # 测试1: 日志管理器功能
    try:
        log_manager = test_log_manager()
        print("✅ 日志管理器测试通过")
    except Exception as e:
        print(f"❌ 日志管理器测试失败: {str(e)}")
    
    # 测试2: GUI界面
    try:
        print(f"\n🎨 启动GUI测试界面...")
        test_gui = create_test_gui()
        
        print("📝 GUI测试说明:")
        print("• 点击各种测试按钮验证功能")
        print("• 观察交易日志和详细日志的区别")
        print("• 验证重复股票过滤功能")
        print("• 测试日志管理功能")
        
        test_gui.mainloop()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {str(e)}")
    
    print(f"\n{'='*60}")
    print("✅ 增强日志系统测试完成")


if __name__ == "__main__":
    main()
