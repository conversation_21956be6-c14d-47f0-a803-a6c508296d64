#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试xtdata连接和行情获取问题
专门检测行情获取失败的原因
"""

import sys
import os
import traceback
from datetime import datetime

def test_xtdata_import():
    """测试xtdata模块导入"""
    print("=" * 60)
    print("1. 测试xtdata模块导入")
    print("=" * 60)
    
    try:
        from xtquant import xtdata
        print("xtdata模块导入成功")
        return True, xtdata
    except ImportError as e:
        print(f"xtdata模块导入失败: {e}")
        return False, None
    except Exception as e:
        print(f"xtdata模块导入异常: {e}")
        return False, None

def test_xtdata_connection(xtdata):
    """测试xtdata连接状态"""
    print("\n" + "=" * 60)
    print("2. 测试xtdata连接状态")
    print("=" * 60)
    
    try:
        # 检查是否有连接方法
        if hasattr(xtdata, 'connect'):
            print("发现xtdata.connect方法")
            try:
                result = xtdata.connect()
                print(f"xtdata.connect()返回值: {result}")
                return True
            except Exception as e:
                print(f"xtdata.connect()调用失败: {e}")
                return False
        else:
            print("xtdata模块没有connect方法")
            
        # 检查其他可能的连接方法
        connection_methods = ['start', 'init', 'login', 'run']
        for method in connection_methods:
            if hasattr(xtdata, method):
                print(f"发现xtdata.{method}方法")
                
        return True
        
    except Exception as e:
        print(f"测试xtdata连接异常: {e}")
        return False

def test_stock_quote_direct(xtdata, stock_codes):
    """直接测试股票行情获取"""
    print("\n" + "=" * 60)
    print("3. 直接测试股票行情获取")
    print("=" * 60)
    
    for stock_code in stock_codes:
        print(f"\n测试股票: {stock_code}")
        print("-" * 40)
        
        try:
            # 测试get_full_tick方法
            print(f"调用 xtdata.get_full_tick(['{stock_code}'])")
            market_data = xtdata.get_full_tick([stock_code])
            
            print(f"返回数据类型: {type(market_data)}")
            print(f"返回数据: {market_data}")
            
            if market_data:
                if stock_code in market_data:
                    data = market_data[stock_code]
                    print(f"股票数据存在，数据类型: {type(data)}")
                    print(f"数据内容: {data}")
                    
                    # 检查关键字段
                    if isinstance(data, dict):
                        key_fields = ['lastPrice', 'askPrice', 'bidPrice', 'volume']
                        for field in key_fields:
                            if field in data:
                                print(f"  {field}: {data[field]}")
                            else:
                                print(f"  {field}: 不存在")
                    
                    print(f"行情获取成功")
                else:
                    print(f"返回数据中不包含股票代码 {stock_code}")
                    print(f"可用的股票代码: {list(market_data.keys())}")
            else:
                print("返回数据为空")
                
        except Exception as e:
            print(f"获取{stock_code}行情失败: {e}")
            print(f"异常详情: {traceback.format_exc()}")

def test_alternative_methods(xtdata, stock_codes):
    """测试其他可能的行情获取方法"""
    print("\n" + "=" * 60)
    print("4. 测试其他行情获取方法")
    print("=" * 60)
    
    # 检查可用的方法
    methods_to_test = [
        'get_market_data',
        'get_instrument_detail', 
        'get_market_data_ex',
        'get_full_tick_data',
        'get_l2_quote',
        'get_divid_factors'
    ]
    
    for method_name in methods_to_test:
        if hasattr(xtdata, method_name):
            print(f"\n发现方法: xtdata.{method_name}")
            method = getattr(xtdata, method_name)
            
            for stock_code in stock_codes[:1]:  # 只测试第一个股票
                try:
                    print(f"  测试 {method_name}('{stock_code}')")
                    if method_name == 'get_instrument_detail':
                        result = method(stock_code)
                    else:
                        result = method([stock_code])
                    
                    print(f"  返回结果: {result}")
                    
                except Exception as e:
                    print(f"  {method_name}调用失败: {e}")
        else:
            print(f"方法不存在: xtdata.{method_name}")

def test_xtdata_status(xtdata):
    """测试xtdata状态信息"""
    print("\n" + "=" * 60)
    print("5. 测试xtdata状态信息")
    print("=" * 60)
    
    # 检查状态相关的属性和方法
    status_attrs = ['__version__', 'version', 'is_connected', 'status']
    
    for attr in status_attrs:
        if hasattr(xtdata, attr):
            try:
                value = getattr(xtdata, attr)
                if callable(value):
                    try:
                        result = value()
                        print(f"xtdata.{attr}(): {result}")
                    except:
                        print(f"xtdata.{attr}: 方法调用失败")
                else:
                    print(f"xtdata.{attr}: {value}")
            except Exception as e:
                print(f"获取xtdata.{attr}失败: {e}")
        else:
            print(f"xtdata.{attr}: 不存在")

def main():
    """主测试函数"""
    print("QMT xtdata 连接和行情获取问题诊断")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试的股票代码（从错误日志中获取）
    test_stocks = ['301230.SZ', '688529.SH', '301230', '688529']
    
    # 1. 测试模块导入
    import_success, xtdata = test_xtdata_import()
    if not import_success:
        print("\n模块导入失败，无法继续测试")
        return
    
    # 2. 测试连接状态
    test_xtdata_connection(xtdata)
    
    # 3. 测试状态信息
    test_xtdata_status(xtdata)
    
    # 4. 直接测试行情获取
    test_stock_quote_direct(xtdata, test_stocks)
    
    # 5. 测试其他方法
    test_alternative_methods(xtdata, test_stocks)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n诊断建议:")
    print("1. 检查QMT客户端是否已登录")
    print("2. 检查QMT数据服务是否启动")
    print("3. 检查网络连接是否正常")
    print("4. 检查账户是否有行情数据权限")
    print("5. 尝试在QMT客户端中手动查看这些股票的行情")

if __name__ == "__main__":
    main()
