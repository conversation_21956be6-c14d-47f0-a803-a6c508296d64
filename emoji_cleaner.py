#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Emoji清理工具
递归扫描目录中的所有文本文件，删除所有emoji字符
保持代码逻辑和文档结构完整性
"""

import os
import re
import shutil
import time
from pathlib import Path
from typing import List, Tuple, Dict

class EmojiCleaner:
    def __init__(self, root_dir: str):
        self.root_dir = Path(root_dir)
        self.backup_dir = self.root_dir / "emoji_cleanup_backup"
        
        # 支持的文本文件扩展名
        self.text_extensions = {
            '.py', '.md', '.txt', '.json', '.log', '.cfg', '.ini', 
            '.yaml', '.yml', '.xml', '.html', '.css', '.js', '.ts',
            '.sql', '.sh', '.bat', '.cmd', '.ps1', '.conf', '.config',
            '.properties', '.env', '.gitignore', '.dockerfile'
        }
        
        # 跳过的二进制文件扩展名
        self.binary_extensions = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat',
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.svg',
            '.mp3', '.mp4', '.avi', '.mov', '.wav', '.pdf', '.zip',
            '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'
        }
        
        # Emoji Unicode范围（完整列表）
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情符号
            r'[\U0001F300-\U0001F5FF]|'  # 杂项符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图符号
            r'[\U0001F1E0-\U0001F1FF]|'  # 区域指示符号
            r'[\*********-\U000026FF]|'  # 杂项符号
            r'[\*********-\U000027BF]|'  # 装饰符号
            r'[\U0001F900-\U0001F9FF]|'  # 补充符号和象形文字
            r'[\U0001FA70-\U0001FAFF]|'  # 符号和象形文字扩展A
            r'[\U00002B50-\U00002B55]|'  # 星星等
            r'[\U0000231A-\U0000231B]|'  # 手表
            r'[\U000023E9-\U000023EC]|'  # 播放按钮
            r'[\U000025AA-\U000025AB]|'  # 方块
            r'[\U000025B6]|'             # 播放按钮
            r'[\U000025C0]|'             # 倒退按钮
            r'[\U000025FB-\U000025FE]|'  # 方块
            r'[\U00002934-\U00002935]|'  # 箭头
            r'[\U00003030]|'             # 波浪线
            r'[\U0000303D]|'             # 部分
            r'[\U00003297]|'             # 祝贺
            r'[\U00003299]'              # 秘密
        )
        
        # 统计信息
        self.stats = {
            'total_files_scanned': 0,
            'total_files_processed': 0,
            'total_emojis_removed': 0,
            'files_with_emojis': 0,
            'skipped_files': 0,
            'error_files': 0
        }
        
        self.processed_files = []
        self.error_files = []
        self.skipped_files = []

    def create_backup_dir(self):
        """创建备份目录"""
        if not self.backup_dir.exists():
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            print(f"创建备份目录: {self.backup_dir}")

    def is_text_file(self, file_path: Path) -> bool:
        """判断是否为文本文件"""
        # 检查扩展名
        if file_path.suffix.lower() in self.binary_extensions:
            return False
        
        if file_path.suffix.lower() in self.text_extensions:
            return True
        
        # 对于无扩展名文件，尝试检测内容
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
                if not sample:
                    return False

                # 尝试常见编码解码
                encodings = ['utf-8', 'gbk', 'ascii', 'latin1']
                for encoding in encodings:
                    try:
                        sample.decode(encoding)
                        return True
                    except UnicodeDecodeError:
                        continue

                return False
        except:
            return False

    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            # 尝试常见编码
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'ascii', 'latin1']

            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)  # 读取前1024字节进行检测

            for encoding in encodings:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue

            return 'utf-8'  # 默认使用UTF-8
        except:
            return 'utf-8'

    def backup_file(self, file_path: Path) -> bool:
        """备份文件"""
        try:
            # 计算相对路径
            rel_path = file_path.relative_to(self.root_dir)
            backup_path = self.backup_dir / rel_path
            
            # 创建备份目录
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(file_path, backup_path)
            return True
        except Exception as e:
            print(f"备份文件失败 {file_path}: {e}")
            return False

    def clean_emojis_from_text(self, text: str) -> Tuple[str, int]:
        """从文本中清理emoji字符"""
        original_text = text
        cleaned_text = self.emoji_pattern.sub('', text)
        
        # 计算删除的emoji数量
        emoji_count = len(original_text) - len(cleaned_text)
        
        return cleaned_text, emoji_count

    def process_file(self, file_path: Path) -> Dict:
        """处理单个文件"""
        result = {
            'path': str(file_path),
            'processed': False,
            'emojis_removed': 0,
            'error': None
        }
        
        try:
            # 检测编码
            encoding = self.detect_encoding(file_path)
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                original_content = f.read()
            
            # 清理emoji
            cleaned_content, emoji_count = self.clean_emojis_from_text(original_content)
            
            # 如果有emoji被删除，则更新文件
            if emoji_count > 0:
                # 备份原文件
                if not self.backup_file(file_path):
                    result['error'] = "备份失败"
                    return result
                
                # 写入清理后的内容
                with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(cleaned_content)
                
                result['processed'] = True
                result['emojis_removed'] = emoji_count
                
                print(f"处理完成: {file_path} (删除 {emoji_count} 个emoji)")
            
        except Exception as e:
            result['error'] = str(e)
            print(f"处理文件出错 {file_path}: {e}")
        
        return result

    def scan_and_clean(self):
        """扫描并清理所有文件"""
        print(f"开始扫描目录: {self.root_dir}")
        print("=" * 60)
        
        # 创建备份目录
        self.create_backup_dir()
        
        # 递归扫描所有文件
        for file_path in self.root_dir.rglob('*'):
            if file_path.is_file():
                self.stats['total_files_scanned'] += 1
                
                # 跳过备份目录中的文件
                if self.backup_dir in file_path.parents:
                    continue
                
                # 跳过隐藏文件和系统文件
                if file_path.name.startswith('.') and file_path.name not in ['.gitignore', '.env']:
                    self.skipped_files.append(str(file_path))
                    self.stats['skipped_files'] += 1
                    continue
                
                # 检查是否为文本文件
                if not self.is_text_file(file_path):
                    self.skipped_files.append(str(file_path))
                    self.stats['skipped_files'] += 1
                    continue
                
                # 处理文件
                result = self.process_file(file_path)
                
                if result['error']:
                    self.error_files.append(result)
                    self.stats['error_files'] += 1
                elif result['processed']:
                    self.processed_files.append(result)
                    self.stats['total_files_processed'] += 1
                    self.stats['total_emojis_removed'] += result['emojis_removed']
                    if result['emojis_removed'] > 0:
                        self.stats['files_with_emojis'] += 1

    def print_report(self):
        """打印处理报告"""
        print("\n" + "=" * 60)
        print("EMOJI清理完成报告")
        print("=" * 60)
        
        print(f"扫描文件总数: {self.stats['total_files_scanned']}")
        print(f"处理文件总数: {self.stats['total_files_processed']}")
        print(f"包含emoji的文件: {self.stats['files_with_emojis']}")
        print(f"删除emoji总数: {self.stats['total_emojis_removed']}")
        print(f"跳过文件总数: {self.stats['skipped_files']}")
        print(f"错误文件总数: {self.stats['error_files']}")
        
        if self.processed_files:
            print(f"\n处理的文件列表 ({len(self.processed_files)} 个):")
            print("-" * 40)
            for file_info in self.processed_files:
                print(f"  {file_info['path']} (删除 {file_info['emojis_removed']} 个emoji)")
        
        if self.error_files:
            print(f"\n错误文件列表 ({len(self.error_files)} 个):")
            print("-" * 40)
            for file_info in self.error_files:
                print(f"  {file_info['path']}: {file_info['error']}")
        
        print(f"\n备份目录: {self.backup_dir}")
        print("所有修改的文件都已备份到上述目录")


def main():
    """主函数"""
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 确认操作
    print("\n警告: 此操作将删除所有文本文件中的emoji字符")
    print("所有修改的文件将被备份到 'emoji_cleanup_backup' 目录")
    
    confirm = input("\n确认继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 创建清理器并执行
    cleaner = EmojiCleaner(current_dir)
    
    start_time = time.time()
    cleaner.scan_and_clean()
    end_time = time.time()
    
    # 打印报告
    cleaner.print_report()
    
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")
    print("清理完成!")


if __name__ == "__main__":
    main()
