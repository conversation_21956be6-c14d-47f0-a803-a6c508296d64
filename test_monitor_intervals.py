#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控间隔功能
验证GUI界面集成和配置保存/加载功能
"""

import sys
import os
import json
import tkinter as tk
from tkinter import ttk

# 添加项目路径到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入窗口管理器
from window_manager import setup_test_window

def test_config_file_operations():
    """测试配置文件操作"""
    print(" 测试配置文件操作")
    print("=" * 50)
    
    config_file = "test_config.json"
    
    # 测试配置保存
    test_config = {
        'account': '********',
        'block_name': '涨停双响炮刚启动剔除',
        'start_time': '09:30:00',
        'end_time': '14:55:00',
        'single_amount': '5000',
        'total_amount': '20000',
        'price_type': '最优五档转限价',
        'price_adjust': '0.5',
        'reserve_money': '10000',
        'trade_interval': '1',
        # 新增监控参数
        'monitor_interval': '2',
        'monitor_unit': '秒',
        'cancel_interval': '15',
        'cancel_unit': '秒'
    }
    
    try:
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=4)
        print(" 配置保存成功")
        
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        # 验证监控参数
        monitor_interval = loaded_config.get('monitor_interval', '1')
        monitor_unit = loaded_config.get('monitor_unit', '秒')
        cancel_interval = loaded_config.get('cancel_interval', '10')
        cancel_unit = loaded_config.get('cancel_unit', '秒')
        
        print(f" 板块监控间隔: {monitor_interval}{monitor_unit}")
        print(f" 撤单任务间隔: {cancel_interval}{cancel_unit}")
        
        # 测试时间转换
        def get_interval_in_seconds(interval_str, unit):
            try:
                interval = float(interval_str)
                if unit == "分钟":
                    return int(interval * 60)
                else:  # 秒
                    return int(interval)
            except ValueError:
                return 1 if unit == "秒" else 60
        
        monitor_seconds = get_interval_in_seconds(monitor_interval, monitor_unit)
        cancel_seconds = get_interval_in_seconds(cancel_interval, cancel_unit)
        
        print(f" 转换后监控间隔: {monitor_seconds}秒")
        print(f" 转换后撤单间隔: {cancel_seconds}秒")
        
        # 清理测试文件
        os.remove(config_file)
        print(" 配置文件测试完成")
        
    except Exception as e:
        print(f" 配置文件测试失败: {str(e)}")

def test_gui_components():
    """测试GUI组件"""
    print(f"\n{'='*50}")
    print(" 测试GUI组件")
    print("=" * 50)
    
    try:
        # 创建测试窗口
        root = tk.Tk()

        # 使用窗口管理器设置窗口
        setup_test_window(root, "监控间隔设置测试")
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="监控间隔设置测试", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="监控参数设置", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 板块监控间隔设置
        ttk.Label(settings_frame, text="板块监控间隔:").grid(row=0, column=0, sticky=tk.W, pady=5)
        monitor_frame = ttk.Frame(settings_frame)
        monitor_frame.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        monitor_interval_entry = ttk.Entry(monitor_frame, width=6)
        monitor_interval_entry.pack(side=tk.LEFT)
        monitor_interval_entry.insert(0, "1")
        
        monitor_unit_combo = ttk.Combobox(monitor_frame, width=4, values=["秒", "分钟"], state="readonly")
        monitor_unit_combo.pack(side=tk.LEFT, padx=(2,0))
        monitor_unit_combo.set("秒")
        
        # 撤单任务间隔设置
        ttk.Label(settings_frame, text="撤单任务间隔:").grid(row=1, column=0, sticky=tk.W, pady=5)
        cancel_frame = ttk.Frame(settings_frame)
        cancel_frame.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        cancel_interval_entry = ttk.Entry(cancel_frame, width=6)
        cancel_interval_entry.pack(side=tk.LEFT)
        cancel_interval_entry.insert(0, "10")
        
        cancel_unit_combo = ttk.Combobox(cancel_frame, width=4, values=["秒", "分钟"], state="readonly")
        cancel_unit_combo.pack(side=tk.LEFT, padx=(2,0))
        cancel_unit_combo.set("秒")
        
        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="当前设置", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        status_text = tk.Text(status_frame, height=6, width=60)
        status_text.pack(fill=tk.BOTH, expand=True)
        
        # 验证函数
        def validate_and_update():
            try:
                # 获取当前设置
                monitor_val = monitor_interval_entry.get()
                monitor_unit = monitor_unit_combo.get()
                cancel_val = cancel_interval_entry.get()
                cancel_unit = cancel_unit_combo.get()
                
                # 验证输入
                monitor_num = float(monitor_val)
                cancel_num = float(cancel_val)
                
                # 转换为秒数
                monitor_seconds = int(monitor_num * 60) if monitor_unit == "分钟" else int(monitor_num)
                cancel_seconds = int(cancel_num * 60) if cancel_unit == "分钟" else int(cancel_num)
                
                # 更新状态显示
                status_text.delete(1.0, tk.END)
                status_text.insert(tk.END, f"板块监控间隔: {monitor_val}{monitor_unit} ({monitor_seconds}秒)\n")
                status_text.insert(tk.END, f"撤单任务间隔: {cancel_val}{cancel_unit} ({cancel_seconds}秒)\n\n")
                
                # 验证范围
                if monitor_unit == "秒" and not (1 <= monitor_num <= 3600):
                    status_text.insert(tk.END, "️ 监控间隔超出范围 (1-3600秒)\n")
                    monitor_interval_entry.config(foreground='red')
                elif monitor_unit == "分钟" and not (1/60 <= monitor_num <= 60):
                    status_text.insert(tk.END, "️ 监控间隔超出范围 (1-60分钟)\n")
                    monitor_interval_entry.config(foreground='red')
                else:
                    monitor_interval_entry.config(foreground='black')
                    status_text.insert(tk.END, " 监控间隔设置有效\n")
                
                if cancel_unit == "秒" and not (5 <= cancel_num <= 1800):
                    status_text.insert(tk.END, "️ 撤单间隔超出范围 (5-1800秒)\n")
                    cancel_interval_entry.config(foreground='red')
                elif cancel_unit == "分钟" and not (5/60 <= cancel_num <= 30):
                    status_text.insert(tk.END, "️ 撤单间隔超出范围 (5秒-30分钟)\n")
                    cancel_interval_entry.config(foreground='red')
                else:
                    cancel_interval_entry.config(foreground='black')
                    status_text.insert(tk.END, " 撤单间隔设置有效\n")
                    
            except ValueError:
                status_text.delete(1.0, tk.END)
                status_text.insert(tk.END, " 输入格式错误，请输入有效数字\n")
        
        # 绑定验证事件
        monitor_interval_entry.bind('<KeyRelease>', lambda e: validate_and_update())
        monitor_unit_combo.bind('<<ComboboxSelected>>', lambda e: validate_and_update())
        cancel_interval_entry.bind('<KeyRelease>', lambda e: validate_and_update())
        cancel_unit_combo.bind('<<ComboboxSelected>>', lambda e: validate_and_update())
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # 测试按钮
        def test_save():
            validate_and_update()
            status_text.insert(tk.END, "\n 配置保存测试完成")
        
        ttk.Button(button_frame, text="测试保存", command=test_save).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT)
        
        # 初始验证
        validate_and_update()
        
        print(" GUI组件创建成功")
        print(" 请在GUI中测试不同的间隔设置")
        print("观察验证和状态更新功能")
        
        # 运行GUI（注释掉以避免阻塞测试）
        # root.mainloop()
        
        # 自动关闭测试窗口
        root.after(2000, root.destroy)  # 2秒后自动关闭
        root.mainloop()
        
    except Exception as e:
        print(f" GUI组件测试失败: {str(e)}")

def test_interval_conversion():
    """测试间隔转换功能"""
    print(f"\n{'='*50}")
    print(" 测试间隔转换功能")
    print("=" * 50)
    
    def get_interval_in_seconds(interval_str, unit):
        try:
            interval = float(interval_str)
            if unit == "分钟":
                return int(interval * 60)
            else:  # 秒
                return int(interval)
        except ValueError:
            return 1 if unit == "秒" else 60
    
    test_cases = [
        ("1", "秒", 1),
        ("30", "秒", 30),
        ("1", "分钟", 60),
        ("2.5", "分钟", 150),
        ("0.5", "分钟", 30),
        ("invalid", "秒", 1),  # 错误输入测试
        ("invalid", "分钟", 60)  # 错误输入测试
    ]
    
    for interval_str, unit, expected in test_cases:
        result = get_interval_in_seconds(interval_str, unit)
        status = "" if result == expected else ""
        print(f"{status} {interval_str}{unit} → {result}秒 (期望: {expected}秒)")

def main():
    """主测试函数"""
    print(" 监控间隔功能集成测试")
    print("=" * 60)
    
    # 测试配置文件操作
    test_config_file_operations()
    
    # 测试间隔转换
    test_interval_conversion()
    
    # 测试GUI组件
    test_gui_components()
    
    print(f"\n{'='*60}")
    print(" 所有测试完成")
    print(" 功能验证:")
    print("    配置文件保存/加载")
    print("    时间单位转换")
    print("    GUI组件创建")
    print("    输入验证")
    print("    状态更新")

if __name__ == "__main__":
    main()
