#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Emoji验证工具
全面检查目录中是否还存在任何emoji字符
验证emoji清理工作的完整性
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple

class EmojiVerifier:
    def __init__(self, root_dir: str):
        self.root_dir = Path(root_dir)
        self.backup_dir = self.root_dir / "emoji_cleanup_backup"
        
        # 支持的文本文件扩展名
        self.text_extensions = {
            '.py', '.md', '.txt', '.json', '.log', '.cfg', '.ini', 
            '.yaml', '.yml', '.xml', '.html', '.css', '.js', '.ts',
            '.sql', '.sh', '.bat', '.cmd', '.ps1', '.conf', '.config',
            '.properties', '.env', '.gitignore', '.dockerfile'
        }
        
        # 跳过的二进制文件扩展名
        self.binary_extensions = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat',
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.svg',
            '.mp3', '.mp4', '.avi', '.mov', '.wav', '.pdf', '.zip',
            '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'
        }
        
        # 完整的Emoji Unicode范围检测
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情符号
            r'[\U0001F300-\U0001F5FF]|'  # 杂项符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图符号
            r'[\U0001F1E0-\U0001F1FF]|'  # 区域指示符号
            r'[\*********-\U000026FF]|'  # 杂项符号
            r'[\*********-\U000027BF]|'  # 装饰符号
            r'[\U0001F900-\U0001F9FF]|'  # 补充符号和象形文字
            r'[\U0001FA70-\U0001FAFF]|'  # 符号和象形文字扩展A
            r'[\U00002B50-\U00002B55]|'  # 星星等
            r'[\U0000231A-\U0000231B]|'  # 手表
            r'[\U000023E9-\U000023EC]|'  # 播放按钮
            r'[\U000025AA-\U000025AB]|'  # 方块
            r'[\U000025B6]|'             # 播放按钮
            r'[\U000025C0]|'             # 倒退按钮
            r'[\U000025FB-\U000025FE]|'  # 方块
            r'[\U00002934-\U00002935]|'  # 箭头
            r'[\U00003030]|'             # 波浪线
            r'[\U0000303D]|'             # 部分
            r'[\U00003297]|'             # 祝贺
            r'[\U00003299]'              # 秘密
        )
        
        # 统计信息
        self.stats = {
            'total_files_scanned': 0,
            'files_with_emojis': 0,
            'total_emojis_found': 0,
            'skipped_files': 0
        }
        
        self.emoji_findings = []  # 存储发现的emoji信息

    def is_text_file(self, file_path: Path) -> bool:
        """判断是否为文本文件"""
        # 检查扩展名
        if file_path.suffix.lower() in self.binary_extensions:
            return False
        
        if file_path.suffix.lower() in self.text_extensions:
            return True
        
        # 对于无扩展名文件，尝试检测内容
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
                if not sample:
                    return False
                
                # 尝试常见编码解码
                encodings = ['utf-8', 'gbk', 'ascii', 'latin1']
                for encoding in encodings:
                    try:
                        sample.decode(encoding)
                        return True
                    except UnicodeDecodeError:
                        continue
                
                return False
        except:
            return False

    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            # 尝试常见编码
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'ascii', 'latin1']
            
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)  # 读取前1024字节进行检测
                
            for encoding in encodings:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            return 'utf-8'  # 默认使用UTF-8
        except:
            return 'utf-8'

    def find_emojis_in_text(self, text: str, file_path: str) -> List[Dict]:
        """在文本中查找emoji字符"""
        findings = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            matches = list(self.emoji_pattern.finditer(line))
            for match in matches:
                emoji_char = match.group()
                start_pos = match.start()
                end_pos = match.end()
                
                # 获取上下文（前后10个字符）
                context_start = max(0, start_pos - 10)
                context_end = min(len(line), end_pos + 10)
                context = line[context_start:context_end]
                
                findings.append({
                    'file': file_path,
                    'line': line_num,
                    'emoji': emoji_char,
                    'position': start_pos,
                    'context': context,
                    'unicode': f"U+{ord(emoji_char):04X}"
                })
        
        return findings

    def scan_file(self, file_path: Path) -> List[Dict]:
        """扫描单个文件中的emoji"""
        try:
            # 检测编码
            encoding = self.detect_encoding(file_path)
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
            
            # 查找emoji
            findings = self.find_emojis_in_text(content, str(file_path))
            
            if findings:
                print(f"发现emoji: {file_path} ({len(findings)}个)")
                for finding in findings:
                    print(f"  行{finding['line']}: {finding['emoji']} ({finding['unicode']}) - {finding['context']}")
            
            return findings
            
        except Exception as e:
            print(f"扫描文件出错 {file_path}: {e}")
            return []

    def scan_directory(self):
        """扫描整个目录"""
        print(f"开始验证目录: {self.root_dir}")
        print("检查是否还存在遗漏的emoji字符...")
        print("=" * 80)
        
        # 递归扫描所有文件
        for file_path in self.root_dir.rglob('*'):
            if file_path.is_file():
                self.stats['total_files_scanned'] += 1
                
                # 跳过备份目录中的文件
                if self.backup_dir in file_path.parents:
                    self.stats['skipped_files'] += 1
                    continue
                
                # 跳过隐藏文件和系统文件
                if file_path.name.startswith('.') and file_path.name not in ['.gitignore', '.env']:
                    self.stats['skipped_files'] += 1
                    continue
                
                # 检查是否为文本文件
                if not self.is_text_file(file_path):
                    self.stats['skipped_files'] += 1
                    continue
                
                # 扫描文件
                findings = self.scan_file(file_path)
                
                if findings:
                    self.emoji_findings.extend(findings)
                    self.stats['files_with_emojis'] += 1
                    self.stats['total_emojis_found'] += len(findings)

    def print_detailed_report(self):
        """打印详细报告"""
        print("\n" + "=" * 80)
        print("EMOJI验证完成报告")
        print("=" * 80)
        
        print(f"扫描文件总数: {self.stats['total_files_scanned']}")
        print(f"跳过文件总数: {self.stats['skipped_files']}")
        print(f"包含emoji的文件: {self.stats['files_with_emojis']}")
        print(f"发现emoji总数: {self.stats['total_emojis_found']}")
        
        if self.stats['total_emojis_found'] == 0:
            print("\n" + "=" * 20)
            print("清理完成，未发现遗漏的emoji字符")
            print("项目中所有emoji字符已成功清理")
            print("=" * 20)
        else:
            print(f"\n发现 {self.stats['total_emojis_found']} 个遗漏的emoji字符")
            print("详细信息:")
            print("-" * 60)
            
            # 按文件分组显示
            files_dict = {}
            for finding in self.emoji_findings:
                file_path = finding['file']
                if file_path not in files_dict:
                    files_dict[file_path] = []
                files_dict[file_path].append(finding)
            
            for file_path, findings in files_dict.items():
                print(f"\n文件: {file_path}")
                for finding in findings:
                    print(f"  行{finding['line']}: '{finding['emoji']}' ({finding['unicode']})")
                    print(f"    上下文: ...{finding['context']}...")

    def print_summary_report(self):
        """打印简要报告"""
        print("\n" + "=" * 60)
        print("验证结果摘要")
        print("=" * 60)
        
        if self.stats['total_emojis_found'] == 0:
            print("状态: [OK] 清理完成")
            print("结果: 未发现任何emoji字符")
            print("确认: 项目emoji清理工作100%完成")
        else:
            print("状态: [FAIL] 发现遗漏")
            print(f"结果: 发现 {self.stats['total_emojis_found']} 个emoji字符")
            print(f"位置: {self.stats['files_with_emojis']} 个文件")
            print("建议: 需要进行补充清理")


def main():
    """主函数"""
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 创建验证器并执行
    verifier = EmojiVerifier(current_dir)
    verifier.scan_directory()
    
    # 打印报告
    verifier.print_detailed_report()
    verifier.print_summary_report()


if __name__ == "__main__":
    main()
