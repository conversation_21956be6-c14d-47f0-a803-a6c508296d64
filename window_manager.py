#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口位置管理系统
提供统一的窗口位置、大小管理功能
"""

import json
import os
import tkinter as tk
from datetime import datetime


class WindowManager:
    """窗口管理器 - 统一管理窗口位置、大小和状态"""
    
    def __init__(self, config_file="window_config.json"):
        self.config_file = config_file
        self.default_config = {
            "main_window": {
                "width": 920,
                "height": 600,
                "x": None,  # None表示居中
                "y": None,
                "min_width": 800,
                "min_height": 500,
                "resizable": True,
                "center_on_startup": True,
                "remember_position": True
            },
            "test_window": {
                "width": 600,
                "height": 400,
                "x": None,
                "y": None,
                "min_width": 500,
                "min_height": 300,
                "resizable": True,
                "center_on_startup": True,
                "remember_position": False
            }
        }
        
    def load_window_config(self):
        """加载窗口配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 合并默认配置，确保所有必要字段存在
                for window_type, default_settings in self.default_config.items():
                    if window_type not in config:
                        config[window_type] = default_settings.copy()
                    else:
                        # 补充缺失的字段
                        for key, value in default_settings.items():
                            if key not in config[window_type]:
                                config[window_type][key] = value
                                
                return config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载窗口配置失败: {str(e)}")
            return self.default_config.copy()
    
    def save_window_config(self, config):
        """保存窗口配置"""
        try:
            # 添加保存时间戳
            config["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存窗口配置失败: {str(e)}")
    
    def setup_window(self, root, window_type="main_window", title=None):
        """设置窗口位置和大小

        Args:
            root: tkinter根窗口
            window_type: 窗口类型 (main_window, test_window等)
            title: 窗口标题
        """
        try:
            # 立即隐藏窗口，防止在设置过程中显示
            was_withdrawn = False
            if root.winfo_viewable():
                root.withdraw()
                was_withdrawn = True

            config = self.load_window_config()
            window_config = config.get(window_type, self.default_config.get(window_type, self.default_config["main_window"]))

            # 设置窗口标题
            if title:
                root.title(title)

            # 获取窗口尺寸
            width = window_config["width"]
            height = window_config["height"]

            # 确保窗口尺寸在合理范围内
            min_width = window_config.get("min_width", 800)
            min_height = window_config.get("min_height", 500)
            width = max(width, min_width)
            height = max(height, min_height)

            # 计算窗口位置（优化版本，避免过早显示）
            if window_config.get("center_on_startup", True) or window_config["x"] is None or window_config["y"] is None:
                x, y = self.center_window_optimized(root, width, height)
            else:
                x, y = window_config["x"], window_config["y"]
                # 验证位置是否在屏幕范围内
                x, y = self.validate_position_optimized(root, x, y, width, height)

            # 应用窗口设置
            root.geometry(f"{width}x{height}+{x}+{y}")

            # 设置最小尺寸
            root.minsize(min_width, min_height)

            # 设置是否可调整大小
            if not window_config.get("resizable", True):
                root.resizable(False, False)

            # 如果窗口之前是可见的，现在重新显示
            if was_withdrawn or not root.winfo_viewable():
                root.deiconify()

            # 如果需要记住位置，绑定窗口关闭事件
            if window_config.get("remember_position", True):
                # 保存原始的关闭处理函数
                original_protocol = root.protocol("WM_DELETE_WINDOW")

                def on_closing():
                    self.save_window_position(root, window_type)
                    if original_protocol and callable(original_protocol):
                        original_protocol()
                    else:
                        root.destroy()

                root.protocol("WM_DELETE_WINDOW", on_closing)

            print(f"窗口设置完成: {window_type} - {width}x{height}+{x}+{y}")

        except Exception as e:
            print(f"设置窗口失败: {str(e)}")
            # 使用默认设置
            root.geometry("920x600")
            self.center_window_simple(root)
    
    def center_window(self, root, width, height):
        """计算窗口居中位置（兼容版本）"""
        try:
            # 更新窗口以获取准确的屏幕尺寸
            root.update_idletasks()

            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()

            # 计算居中位置
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            # 确保窗口不会超出屏幕边界
            x = max(0, min(x, screen_width - width))
            y = max(0, min(y, screen_height - height))

            return x, y
        except Exception as e:
            print(f"计算居中位置失败: {str(e)}")
            return 100, 50  # 默认位置

    def center_window_optimized(self, root, width, height):
        """优化的窗口居中位置计算（避免过早显示窗口）"""
        try:
            # 不调用update_idletasks，直接获取屏幕尺寸
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()

            # 计算居中位置
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            # 确保窗口不会超出屏幕边界
            x = max(0, min(x, screen_width - width))
            y = max(0, min(y, screen_height - height))

            return x, y
        except Exception as e:
            print(f"优化居中位置计算失败: {str(e)}")
            return 100, 50  # 默认位置
    
    def center_window_simple(self, root):
        """简单的窗口居中方法"""
        try:
            root.update_idletasks()
            width = root.winfo_width()
            height = root.winfo_height()
            x, y = self.center_window(root, width, height)
            root.geometry(f"+{x}+{y}")
        except Exception as e:
            print(f"简单居中失败: {str(e)}")
    
    def validate_position(self, root, x, y, width, height):
        """验证窗口位置是否在屏幕范围内"""
        try:
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()

            # 确保窗口至少有一部分在屏幕内
            x = max(-width + 100, min(x, screen_width - 100))
            y = max(0, min(y, screen_height - 100))

            return x, y
        except Exception as e:
            print(f"验证位置失败: {str(e)}")
            return x, y

    def validate_position_optimized(self, root, x, y, width, height):
        """优化的窗口位置验证（避免触发窗口显示）"""
        try:
            # 直接获取屏幕尺寸，不调用可能触发显示的方法
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()

            # 确保窗口至少有一部分在屏幕内
            x = max(-width + 100, min(x, screen_width - 100))
            y = max(0, min(y, screen_height - 100))

            return x, y
        except Exception as e:
            print(f"优化位置验证失败: {str(e)}")
            return x, y
    
    def save_window_position(self, root, window_type):
        """保存窗口位置和大小"""
        try:
            # 获取当前窗口几何信息
            geometry = root.geometry()
            
            # 解析geometry字符串 "920x600+100+50"
            if '+' in geometry:
                size_part, pos_part = geometry.split('+', 1)
                if '+' in pos_part:
                    x_str, y_str = pos_part.split('+', 1)
                else:
                    # 处理负坐标的情况
                    parts = pos_part.split('-')
                    if len(parts) == 2:
                        x_str = '-' + parts[1]
                        y_str = '0'
                    else:
                        x_str, y_str = '0', '0'
            else:
                size_part = geometry
                x_str, y_str = '0', '0'
            
            # 解析尺寸
            if 'x' in size_part:
                width_str, height_str = size_part.split('x')
            else:
                width_str, height_str = '920', '600'
            
            # 加载现有配置
            config = self.load_window_config()
            
            # 更新窗口配置
            if window_type not in config:
                config[window_type] = self.default_config.get(window_type, {})
            
            config[window_type].update({
                "width": int(width_str),
                "height": int(height_str),
                "x": int(x_str),
                "y": int(y_str)
            })
            
            # 保存配置
            self.save_window_config(config)
            
            print(f"窗口位置已保存: {window_type} - {geometry}")
            
        except Exception as e:
            print(f"保存窗口位置失败: {str(e)}")
    
    def get_window_config(self, window_type="main_window"):
        """获取指定窗口类型的配置"""
        config = self.load_window_config()
        return config.get(window_type, self.default_config.get(window_type, {}))
    
    def update_window_config(self, window_type, **kwargs):
        """更新窗口配置"""
        config = self.load_window_config()
        
        if window_type not in config:
            config[window_type] = self.default_config.get(window_type, {}).copy()
        
        config[window_type].update(kwargs)
        self.save_window_config(config)
    
    def reset_window_config(self, window_type=None):
        """重置窗口配置为默认值"""
        if window_type:
            config = self.load_window_config()
            config[window_type] = self.default_config.get(window_type, {}).copy()
            self.save_window_config(config)
        else:
            # 重置所有窗口配置
            self.save_window_config(self.default_config.copy())


# 全局窗口管理器实例
window_manager = WindowManager()


def setup_main_window(root, title="应用程序"):
    """设置主窗口的便捷函数"""
    window_manager.setup_window(root, "main_window", title)


def setup_test_window(root, title="测试窗口"):
    """设置测试窗口的便捷函数"""
    window_manager.setup_window(root, "test_window", title)


def center_existing_window(root):
    """居中现有窗口的便捷函数"""
    window_manager.center_window_simple(root)


def reset_window_to_default(root, window_type="main_window", title=None):
    """重置窗口到默认设置的便捷函数"""
    window_manager.setup_window(root, window_type, title)


def get_default_window_size(window_type="main_window"):
    """获取默认窗口尺寸的便捷函数"""
    config = window_manager.get_window_config(window_type)
    return config.get("width", 920), config.get("height", 600)
