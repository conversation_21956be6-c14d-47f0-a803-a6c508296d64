#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清空日志功能修复
验证clear_log方法是否正常工作
"""

import sys
import os
import tkinter as tk
import tkinter.ttk as ttk

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'qmt_ths'))

def test_clear_log_functionality():
    """测试清空日志功能"""
    print(" 测试清空日志功能修复")
    print("=" * 50)
    
    try:
        # 创建简单的测试GUI
        root = tk.Tk()
        root.title("清空日志功能测试")
        root.geometry("600x400")
        
        # 创建日志文本框
        simplified_log_text = tk.Text(root, height=8, width=70)
        simplified_log_text.pack(pady=5)
        simplified_log_text.insert(tk.E<PERSON>, "这是交易日志内容\n")
        simplified_log_text.insert(tk.END, "测试日志1\n")
        simplified_log_text.insert(tk.END, "测试日志2\n")
        
        detailed_log_text = tk.Text(root, height=8, width=70)
        detailed_log_text.pack(pady=5)
        detailed_log_text.insert(tk.END, "这是详细日志内容\n")
        detailed_log_text.insert(tk.END, "详细测试日志1\n")
        detailed_log_text.insert(tk.END, "详细测试日志2\n")
        
        # 模拟修复后的clear_log方法
        def test_clear_log():
            try:
                # 清空所有日志显示
                if simplified_log_text:
                    simplified_log_text.delete(1.0, tk.END)
                
                if detailed_log_text:
                    detailed_log_text.delete(1.0, tk.END)

                # 模拟增强日志管理器为None的情况
                enhanced_log_manager = None
                if enhanced_log_manager:
                    try:
                        enhanced_log_manager.clear_all_logs()
                    except AttributeError:
                        # 如果没有clear_all_logs方法，尝试其他清空方法
                        if hasattr(enhanced_log_manager, 'clear_simplified_logs'):
                            enhanced_log_manager.clear_simplified_logs()
                        if hasattr(enhanced_log_manager, 'clear_detailed_logs'):
                            enhanced_log_manager.clear_detailed_logs()

                # 添加确认消息
                simplified_log_text.insert(tk.END, "所有日志已清空\n")
                print(" 清空日志功能测试成功")
                
            except Exception as e:
                print(f" 清空日志功能测试失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 创建按钮
        button_frame = tk.Frame(root)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="测试清空日志", command=test_clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT, padx=5)
        
        # 添加说明
        info_label = tk.Label(root, text="点击'测试清空日志'按钮验证功能是否正常", 
                             wraplength=500, justify=tk.LEFT)
        info_label.pack(pady=5)
        
        print(" 测试GUI创建成功")
        print(" 测试说明：")
        print("1. 点击'测试清空日志'按钮")
        print("2. 观察日志是否被清空")
        print("3. 检查是否出现错误")
        
        # 运行GUI
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f" 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_code_analysis():
    """分析修复后的代码"""
    print(f"\n分析修复后的代码")
    print("=" * 50)
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 检查修复后的clear_log方法
        import re
        clear_log_pattern = r'def clear_log\(.*?\):(.*?)(?=def |\Z)'
        clear_log_match = re.search(clear_log_pattern, source_code, re.DOTALL)
        
        if clear_log_match:
            method_code = clear_log_match.group(1)
            
            # 检查关键修复点
            checks = [
                ("hasattr检查simplified_log_text", "hasattr(self, 'simplified_log_text')" in method_code),
                ("hasattr检查detailed_log_text", "hasattr(self, 'detailed_log_text')" in method_code),
                ("hasattr检查enhanced_log_manager", "hasattr(self, 'enhanced_log_manager')" in method_code),
                ("AttributeError异常处理", "except AttributeError:" in method_code),
                ("使用_safe_log_message", "_safe_log_message" in method_code)
            ]
            
            print("修复检查结果:")
            all_passed = True
            for check_name, result in checks:
                status = "" if result else ""
                print(f"  {status} {check_name}")
                if not result:
                    all_passed = False
            
            if all_passed:
                print(f"\n 所有修复点都已正确实现")
                return True
            else:
                print(f"\n 部分修复点缺失")
                return False
        else:
            print(" 未找到clear_log方法")
            return False
            
    except Exception as e:
        print(f" 代码分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print(" 清空日志功能修复测试")
    print("=" * 60)
    print("验证clear_log方法修复是否成功")
    print("=" * 60)
    
    # 代码分析
    code_analysis_result = test_code_analysis()
    
    # 功能测试
    if code_analysis_result:
        print(f"\n 启动功能测试GUI...")
        functionality_result = test_clear_log_functionality()
    else:
        print(f"\n️ 代码分析未通过，跳过功能测试")
        functionality_result = False
    
    # 总结
    print(f"\n{'='*60}")
    print(" 测试结果总结")
    print("=" * 60)
    
    print(f"代码分析: {' 通过' if code_analysis_result else ' 失败'}")
    print(f"功能测试: {' 通过' if functionality_result else ' 失败'}")
    
    if code_analysis_result and functionality_result:
        print(f"\n 清空日志功能修复成功！")
        print(" 'NoneType' object has no attribute 'clear_all_logs' 错误已修复")
        print(" 现在可以正常使用清空日志功能")
    elif code_analysis_result:
        print(f"\n️ 代码修复正确，但需要在实际环境中验证")
    else:
        print(f"\n 修复可能不完整，需要进一步检查")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
