#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价格验证和交易时间检查功能测试脚本

测试内容：
1. 价格验证功能测试
2. 交易时间检查功能测试
3. 配置加载和保存测试
"""

import json
import os
import sys
import unittest
from datetime import datetime
from unittest.mock import Mock, patch

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tonghuashun_gui import TongHuaShunTrader


class TestPriceValidationAndTradingTime(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        # 创建一个模拟的交易器实例
        self.trader = TongHuaShunTrader.__new__(TongHuaShunTrader)
        self.trader.config_file = "test_trader_config.json"
        
        # 初始化必要的属性
        self.trader.start_time = Mock()
        self.trader.end_time = Mock()
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
    def tearDown(self):
        """测试后清理"""
        # 删除测试配置文件
        if os.path.exists(self.trader.config_file):
            os.remove(self.trader.config_file)
    
    def test_price_validation_chuangye_normal(self):
        """测试创业板正常价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "300001.SZ", 30.0, "300001.SZ")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
    
    def test_price_validation_chuangye_too_high(self):
        """测试创业板价格过高验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "300001.SZ", 600.0, "300001.SZ")
        self.assertFalse(is_valid)
        self.assertIn("创业板", message)
        self.assertIn("超过设定上限", message)
    
    def test_price_validation_kechuang_normal(self):
        """测试科创板正常价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "688001.SH", 150.0, "688001.SH")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
    
    def test_price_validation_kechuang_too_high(self):
        """测试科创板价格过高验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "688001.SH", 1500.0, "688001.SH")
        self.assertFalse(is_valid)
        self.assertIn("科创板", message)
        self.assertIn("超过设定上限", message)
    
    def test_price_validation_zhuban_normal(self):
        """测试主板正常价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "600001.SH", 20.0, "600001.SH")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
    
    def test_price_validation_zhuban_too_high(self):
        """测试主板价格过高验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "600001.SH", 1500.0, "600001.SH")
        self.assertFalse(is_valid)
        self.assertIn("主板", message)
        self.assertIn("超过设定上限", message)
    
    def test_price_validation_beijiao_normal(self):
        """测试北交所正常价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "833333.BJ", 15.0, "833333.BJ")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
    
    def test_price_validation_beijiao_too_high(self):
        """测试北交所价格过高验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "833333.BJ", 300.0, "833333.BJ")
        self.assertFalse(is_valid)
        self.assertIn("北交所", message)
        self.assertIn("超过设定上限", message)
    
    def test_price_validation_st_stock(self):
        """测试ST股票价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "*ST001.SZ", 3.0, "*ST001.SZ")
        self.assertTrue(is_valid)
    
    def test_price_validation_st_stock_too_high(self):
        """测试ST股票价格过高验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "*ST001.SZ", 60.0, "*ST001.SZ")
        self.assertFalse(is_valid)
        self.assertIn("ST股票", message)
        self.assertIn("超过设定上限", message)
    
    def test_price_validation_negative_price(self):
        """测试负价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "600001.SH", -10.0, "600001.SH")
        self.assertFalse(is_valid)
        self.assertIn("价格不能为0或负数", message)
    
    def test_price_validation_zero_price(self):
        """测试零价格验证"""
        is_valid, message = self.trader._validate_price_reasonableness(
            "600001.SH", 0.0, "600001.SH")
        self.assertFalse(is_valid)
        self.assertIn("价格不能为0或负数", message)
    
    def test_trading_time_during_trading_hours(self):
        """测试交易时间内检查"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试上午10点是否在交易时间内
        is_trading = self.trader.is_trading_time("10:00:00")
        self.assertTrue(is_trading)
    
    def test_trading_time_before_opening(self):
        """测试开盘前时间检查"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试上午8点是否在交易时间内
        is_trading = self.trader.is_trading_time("08:00:00")
        self.assertFalse(is_trading)
    
    def test_trading_time_after_closing(self):
        """测试收盘后时间检查"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试下午15点是否在交易时间内
        is_trading = self.trader.is_trading_time("15:00:00")
        self.assertFalse(is_trading)
    
    def test_trading_time_at_opening_time(self):
        """测试开盘时间检查"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试正好9:30是否在交易时间内
        is_trading = self.trader.is_trading_time("09:30:00")
        self.assertTrue(is_trading)
    
    def test_trading_time_at_closing_time(self):
        """测试收盘时间检查"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试正好14:55是否在交易时间内
        is_trading = self.trader.is_trading_time("14:55:00")
        self.assertTrue(is_trading)
    
    def test_trading_time_invalid_format(self):
        """测试无效时间格式处理"""
        # 设置交易时间为9:30-14:55（默认）
        self.trader.start_time.get.return_value = "09:30:00"
        self.trader.end_time.get.return_value = "14:55:00"
        
        # 测试无效时间格式，默认允许交易
        is_trading = self.trader.is_trading_time("invalid_time")
        self.assertTrue(is_trading)  # 出错时默认允许交易
    
    def test_config_save_and_load_price_limits(self):
        """测试配置保存和加载价格限制"""
        # 创建测试配置
        test_config = {
            "price_limits": {
                "default": 2000,
                "chuangye": 400,
                "kechuang": 800,
                "zhuban": 800,
                "beijiao": 150,
                "st": 30,
                "index": 4000,
                "hk": 800,
                "us": 8000,
                "futures": 80000
            }
        }
        
        # 保存测试配置
        with open(self.trader.config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=4)
        
        # 测试价格验证是否使用了自定义配置
        is_valid, message = self.trader._validate_price_reasonableness(
            "300001.SZ", 350.0, "300001.SZ")
        self.assertTrue(is_valid)  # 350 < 400 (自定义创业板上限)
        
        is_valid, message = self.trader._validate_price_reasonableness(
            "300001.SZ", 450.0, "300001.SZ")
        self.assertFalse(is_valid)  # 450 > 400 (自定义创业板上限)
        self.assertIn("创业板", message)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)