#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度日志系统分析
分析日志功能失效和重复问题的原因
"""

import os
import re
import sys

def analyze_log_system_status():
    """分析日志系统当前状态"""
    print("深度分析日志系统状态")
    print("=" * 50)
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f" 无法读取源代码: {e}")
        return False
    
    # 检查关键日志方法
    log_methods = [
        '_safe_log_message',
        '_should_filter_from_simplified_log',
        'log_message',
        'clear_log',
        'clear_simplified_log',
        'clear_detailed_log'
    ]
    
    print("检查日志方法存在性:")
    missing_methods = []
    for method in log_methods:
        pattern = rf'def {method}\('
        if re.search(pattern, source_code):
            print(f"   {method}")
        else:
            print(f"   {method}")
            missing_methods.append(method)
    
    if missing_methods:
        print(f"\n 缺少关键方法: {missing_methods}")
        return False
    
    # 检查_safe_log_message的实现
    print(f"\n检查_safe_log_message实现:")
    safe_log_pattern = r'def _safe_log_message\(.*?\):(.*?)(?=def |\Z)'
    safe_log_match = re.search(safe_log_pattern, source_code, re.DOTALL)
    
    if safe_log_match:
        method_code = safe_log_match.group(1)
        
        # 检查关键实现点
        checks = [
            ("更新详细日志", "detailed_log_text.insert" in method_code),
            ("更新简化日志", "simplified_log_text.insert" in method_code),
            ("调用过滤方法", "_should_filter_from_simplified_log" in method_code),
            ("异常处理", "except" in method_code),
            ("hasattr检查", "hasattr" in method_code)
        ]
        
        for check_name, result in checks:
            status = "" if result else ""
            print(f"  {status} {check_name}")
    else:
        print("   _safe_log_message方法未找到")
        return False
    
    # 检查log_message方法是否调用_safe_log_message
    print(f"\n检查log_message调用链:")
    log_message_pattern = r'def log_message\(.*?\):(.*?)(?=def |\Z)'
    log_message_match = re.search(log_message_pattern, source_code, re.DOTALL)
    
    if log_message_match:
        method_code = log_message_match.group(1)
        if "_safe_log_message" in method_code:
            print("   log_message调用_safe_log_message")
        else:
            print("   log_message未调用_safe_log_message")
            print("  log_message实际调用:")
            # 查找实际调用的内容
            lines = method_code.strip().split('\n')
            for line in lines[:10]:  # 只显示前10行
                if line.strip():
                    print(f"    {line.strip()}")
    else:
        print("   log_message方法未找到")
    
    return True

def analyze_clear_log_duplication():
    """分析清空日志重复问题"""
    print(f"\n分析清空日志重复问题")
    print("=" * 50)
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f" 无法读取源代码: {e}")
        return False
    
    # 检查clear_log方法
    clear_log_pattern = r'def clear_log\(.*?\):(.*?)(?=def |\Z)'
    clear_log_match = re.search(clear_log_pattern, source_code, re.DOTALL)
    
    if clear_log_match:
        method_code = clear_log_match.group(1)
        
        print("clear_log方法分析:")
        
        # 查找所有日志记录调用
        log_calls = []
        if "self.log_message" in method_code:
            log_calls.append("self.log_message")
        if "_safe_log_message" in method_code:
            log_calls.append("_safe_log_message")
        if "self._safe_log_message" in method_code:
            log_calls.append("self._safe_log_message")
        
        print(f"  发现的日志调用: {log_calls}")
        
        # 检查是否有多个日志调用
        if len(log_calls) > 1:
            print("  ️ 发现多个日志调用，可能导致重复")
        elif len(log_calls) == 1:
            print("   只有一个日志调用")
        else:
            print("   没有找到日志调用")
        
        # 检查具体的日志调用行
        lines = method_code.split('\n')
        log_lines = []
        for i, line in enumerate(lines):
            if any(call in line for call in ["log_message", "_safe_log_message"]):
                log_lines.append((i+1, line.strip()))
        
        if log_lines:
            print("  具体的日志调用行:")
            for line_num, line_content in log_lines:
                print(f"    行{line_num}: {line_content}")
    else:
        print(" clear_log方法未找到")
        return False
    
    # 检查是否有事件绑定重复
    print(f"\n检查事件绑定:")
    button_bindings = re.findall(r'command=self\.clear_log', source_code)
    print(f"  发现clear_log绑定次数: {len(button_bindings)}")
    
    if len(button_bindings) > 1:
        print("  ️ 可能存在重复绑定")
    
    return True

def check_gui_initialization():
    """检查GUI初始化"""
    print(f"\n检查GUI初始化")
    print("=" * 50)
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f" 无法读取源代码: {e}")
        return False
    
    # 检查日志组件初始化
    gui_components = [
        'simplified_log_text',
        'detailed_log_text',
        'log_notebook'
    ]
    
    print("检查GUI组件初始化:")
    for component in gui_components:
        if f"self.{component}" in source_code:
            print(f"   {component}")
        else:
            print(f"   {component}")
    
    # 检查_setup_log_callbacks调用
    if "_setup_log_callbacks" in source_code:
        print("   _setup_log_callbacks存在")
        
        # 查找调用位置
        setup_pattern = r'self\._setup_log_callbacks\(\)'
        if re.search(setup_pattern, source_code):
            print("   _setup_log_callbacks被调用")
        else:
            print("   _setup_log_callbacks未被调用")
    else:
        print("   _setup_log_callbacks不存在")
    
    return True

def find_potential_issues():
    """查找潜在问题"""
    print(f"\n查找潜在问题")
    print("=" * 50)
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f"无法读取源代码: {e}")
        return False
    
    potential_issues = []
    
    # 检查是否有冲突的日志方法
    if source_code.count("def log_message(") > 1:
        potential_issues.append("发现多个log_message方法定义")
    
    # 检查是否有未处理的异常
    if "_safe_log_message" in source_code:
        safe_log_pattern = r'def _safe_log_message\(.*?\):(.*?)(?=def |\Z)'
        safe_log_match = re.search(safe_log_pattern, source_code, re.DOTALL)
        if safe_log_match:
            method_code = safe_log_match.group(1)
            if "except Exception" not in method_code and "except:" not in method_code:
                potential_issues.append("_safe_log_message缺少异常处理")
    
    # 检查是否有循环调用
    if "self.log_message" in source_code and "_safe_log_message" in source_code:
        # 检查log_message是否调用_safe_log_message，_safe_log_message是否又调用log_message
        log_message_pattern = r'def log_message\(.*?\):(.*?)(?=def |\Z)'
        log_message_match = re.search(log_message_pattern, source_code, re.DOTALL)
        if log_message_match:
            log_message_code = log_message_match.group(1)
            safe_log_pattern = r'def _safe_log_message\(.*?\):(.*?)(?=def |\Z)'
            safe_log_match = re.search(safe_log_pattern, source_code, re.DOTALL)
            if safe_log_match:
                safe_log_code = safe_log_match.group(1)
                if "_safe_log_message" in log_message_code and "self.log_message" in safe_log_code:
                    potential_issues.append("可能存在log_message和_safe_log_message的循环调用")
    
    # 检查GUI组件访问安全性
    unsafe_patterns = [
        r'self\..*_text\.insert\(.*\)',
        r'self\..*_text\.delete\(.*\)'
    ]
    
    for pattern in unsafe_patterns:
        matches = re.findall(pattern, source_code)
        if len(matches) > 10:  # 如果有很多直接访问
            potential_issues.append(f"发现大量直接GUI访问: {len(matches)}次")
    
    if potential_issues:
        print("️ 发现潜在问题:")
        for i, issue in enumerate(potential_issues, 1):
            print(f"  {i}. {issue}")
    else:
        print(" 未发现明显的潜在问题")
    
    return len(potential_issues) == 0

def main():
    """主分析函数"""
    print(" 深度日志系统分析")
    print("=" * 60)
    print("分析日志功能失效和重复问题的原因")
    print("=" * 60)
    
    # 分析日志系统状态
    log_system_ok = analyze_log_system_status()
    
    # 分析清空日志重复问题
    clear_log_ok = analyze_clear_log_duplication()
    
    # 检查GUI初始化
    gui_init_ok = check_gui_initialization()
    
    # 查找潜在问题
    no_issues = find_potential_issues()
    
    print(f"\n{'='*60}")
    print(" 分析结果总结")
    print("=" * 60)
    
    print(f"日志系统状态: {' 正常' if log_system_ok else ' 异常'}")
    print(f"清空日志功能: {' 正常' if clear_log_ok else ' 异常'}")
    print(f"GUI初始化: {' 正常' if gui_init_ok else ' 异常'}")
    print(f"潜在问题: {' 无' if no_issues else ' 有'}")
    
    if all([log_system_ok, clear_log_ok, gui_init_ok, no_issues]):
        print(f"\n 初步结论: 代码结构正常，问题可能在运行时")
        print(" 建议:")
        print("  1. 检查程序运行时的实际行为")
        print("  2. 查看是否有线程竞争问题")
        print("  3. 检查GUI组件是否正确初始化")
        print("  4. 验证事件绑定是否正确")
    else:
        print(f"\n 发现问题: 代码结构存在问题")
        print(" 需要修复:")
        if not log_system_ok:
            print("  - 修复日志系统")
        if not clear_log_ok:
            print("  - 修复清空日志功能")
        if not gui_init_ok:
            print("  - 修复GUI初始化")
        if not no_issues:
            print("  - 解决潜在问题")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
