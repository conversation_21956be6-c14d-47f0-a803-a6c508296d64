#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读取同花顺StockBlock.ini文件并解析内容，获取指定板块的股票代码
"""

import os
import re
import logging

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('read_block')

def read_stock_block_ini(ths_path):
    """读取同花顺StockBlock.ini文件并返回内容"""
    # 拼接完整的文件路径
    file_path = os.path.join(ths_path, "StockBlock.ini")
    
    try:
        with open(file_path, 'r', encoding='gbk') as f:
            content = f.read()
        logger.info(f"成功读取StockBlock.ini文件: {file_path}")
        return content
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"使用UTF-8编码成功读取StockBlock.ini文件: {file_path}")
            return content
        except Exception as e:
            logger.error(f"使用UTF-8编码读取文件时出错: {e}")
            return None
    except Exception as e:
        logger.error(f"读取文件时出错: {e}")
        return None

def convert_stock_code_format(stock_codes):
    """转换股票代码格式，将市场前缀转换为后缀格式"""
    market_map = {
        # 沪市
        '20': '.SH',  # 沪市ETF、LOF等
        '17': '.SH',  # 沪市主板
        '19': '.SH',  # 沪市可转债
        '22': '.SH',  # 沪市B股
        '18': '.SH',  # 沪市科创板

        # 深市
        '36': '.SZ',  # 深市ETF、LOF等
        '33': '.SZ',  # 深市主板
        '35': '.SZ',  # 深市可转债
        '39': '.OT',  # 其他金融产品(更新映射)
        '30': '.SZ',  # 深市创业板

        # 北交所
        '81': '.BJ',  # 北交所8开头
        '82': '.BJ',  # 北交所4开头
        '83': '.BJ',  # 北交所9开头
        '84': '.BJ',  # 北交所其他

        # 新三板
        '85': '.NQ',  # 新三板
        '86': '.NQ',  # 新三板其他
        '105': '.BJ', # 北交所/新三板混合（需特殊处理）

        # 港股
        '79': '.HK',  # 港股
        '76': '.HK',  # 港股
        '77': '.HK',  # 港股其他
        '78': '.HK',  # 港股ETF

        # 美股
        '87': '.US',  # 美股
        '71': '.US',  # 美股纳斯达克
        '70': '.US',  # 美股其他
        '72': '.US',  # 美股NYSE
        '73': '.US',  # 美股AMEX

        # 期货
        '90': '.FT',  # 商品期货
        '91': '.FT',  # 金融期货
        '92': '.FT',  # 期权
        '93': '.FT',  # 其他期货

        # 指数
        '48': '.IDX',  # 行业指数
        '49': '.IDX',  # 概念指数
        '80': '.IDX',  # 指数
        '88': '.IDX',  # 国际指数

        # 其他
        '97': '.FX',   # 外汇
        '16': '.FD',   # 基金
    }
    
    converted_codes = []

    for code in stock_codes:
        if ':' in code:
            market_code, stock_code = code.split(':', 1)

            # 处理负号前缀的市场代码（如-105, -71, -39）
            original_market_code = market_code
            if market_code.startswith('-'):
                market_code = market_code[1:]  # 去掉负号

            # 特殊处理港股，去掉HK前缀
            if market_code in ['79', '76', '77', '78'] and stock_code.startswith('HK'):
                stock_code = stock_code[2:]

            # 特殊处理北交所股票代码（优先使用.BJ后缀格式）
            if market_code in ['81', '82', '83', '84']:
                # 北交所股票代码可能是8开头、4开头、9开头的纯数字
                if stock_code.isdigit() and len(stock_code) == 6:
                    if stock_code.startswith(('8', '4', '9')):
                        # 只使用.BJ格式，因为已验证QMT支持此格式
                        converted_codes.append(f"{stock_code}.BJ")  # 带后缀格式
                        continue

            # 特殊处理代码105（北交所/新三板混合）
            if market_code == '105':
                # 根据股票代码特征判断是北交所还是新三板
                if stock_code.isdigit() and len(stock_code) == 6:
                    # 北交所股票：8开头、9开头
                    if stock_code.startswith(('8', '9')):
                        converted_codes.append(f"{stock_code}.BJ")  # 北交所格式（只使用.BJ后缀）
                    # 4开头的股票需要进一步判断：43开头的是新三板，其他4开头的是北交所
                    elif stock_code.startswith('4'):
                        if stock_code.startswith('43'):
                            converted_codes.append(f"{stock_code}.NQ")  # 新三板格式
                        else:
                            converted_codes.append(f"{stock_code}.BJ")  # 北交所格式（只使用.BJ后缀）
                    else:
                        # 其他数字开头的认为是新三板
                        converted_codes.append(f"{stock_code}.NQ")  # 新三板格式
                else:
                    # 非6位纯数字的默认为新三板
                    converted_codes.append(f"{stock_code}.NQ")
                continue

            suffix = market_map.get(market_code, '')
            if suffix:
                converted_codes.append(f"{stock_code}{suffix}")
            else:
                # 如果无法处理，保留原始代码
                converted_codes.append(code)
        else:
            # 处理无前缀的股票代码
            # 检查是否为北交所股票（8开头、4开头、9开头的6位数字）
            if code.isdigit() and len(code) == 6 and code.startswith(('8', '4', '9')):
                converted_codes.append(code)  # 保持原格式
            else:
                converted_codes.append(code)

    # 去除重复的股票代码，保持顺序
    unique_codes = []
    seen = set()
    for code in converted_codes:
        if code not in seen:
            unique_codes.append(code)
            seen.add(code)

    return unique_codes

def get_stocks_by_block_name(block_name, ths_path, convert_format=True):
    """
    根据板块名称获取对应的股票代码列表
    
    Args:
        block_name: 板块名称，如"右则交易"
        ths_path: 同花顺安装目录路径，如"D:\\RJ\\10jqka\\同花顺\\mx_148114686"
        convert_format: 是否转换股票代码格式，默认为True
    
    Returns:
        股票代码列表，如果未找到板块则返回空列表
    """
    # 检查目录是否存在
    if not os.path.exists(ths_path):
        return []
    
    # 拼接完整的文件路径
    file_path = os.path.join(ths_path, "StockBlock.ini")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return []
    
    content = read_stock_block_ini(ths_path)
    if not content:
        return []
    
    # 解析板块名称映射表
    block_name_map = {}
    pattern = r'\[BLOCK_NAME_MAP_TABLE\](.*?)(?=\[|$)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        block_section = match.group(1).strip()
        for line in block_section.split('\n'):
            if '=' in line:
                code, name = line.split('=', 1)
                block_name_map[code.strip()] = name.strip()
    
    # 查找板块代码
    block_code = None
    for code, name in block_name_map.items():
        if name == block_name:
            block_code = code
            break
    
    if not block_code:
        return []
    
    # 解析板块股票内容
    block_stock_context = {}
    pattern = r'\[BLOCK_STOCK_CONTEXT\](.*?)(?=\[|$)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        stock_section = match.group(1).strip()
        for line in stock_section.split('\n'):
            if '=' in line:
                code, stocks = line.split('=', 1)
                stock_list = [s for s in stocks.strip().split(',') if s]
                block_stock_context[code.strip()] = stock_list
    
    stock_codes = block_stock_context.get(block_code, [])
    
    # 如果需要转换格式
    if convert_format and stock_codes:
        return convert_stock_code_format(stock_codes)
    
    return stock_codes



