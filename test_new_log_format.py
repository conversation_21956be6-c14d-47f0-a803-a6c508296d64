#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的日志格式
验证股票状态信息显示是否符合预期格式
"""

import sys
import os

# 添加项目路径到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from xtquant import xtdata
    print("✅ 成功导入 xtdata 模块")
except ImportError as e:
    print(f"❌ 导入 xtdata 模块失败: {e}")
    print("请确保QMT已正确安装并配置")
    sys.exit(1)

class MockTrader:
    """模拟交易器类，用于测试日志格式"""
    
    def __init__(self):
        self.price_adjust = MockEntry("0.5")  # 模拟价格调整输入框
    
    def _generate_stock_status_info(self, stock_code, stock_name, current_price, upper_limit, lower_limit, ask_price, bid_price):
        """
        生成统一格式的股票状态信息
        格式：股票代码(股票名称): 价格 + 状态描述 + 流动性 + 委托价格
        """
        try:
            # 提取股票代码（去掉后缀）
            base_code = stock_code.split('.')[0] if '.' in stock_code else stock_code
            
            # 判断股票状态
            status_desc = ""
            liquidity_desc = ""
            order_price = current_price
            
            # 判断涨停/跌停状态
            if upper_limit > 0 and lower_limit > 0:
                if abs(current_price - upper_limit) < 0.01:  # 涨停
                    status_desc = "涨停封板"
                    # 计算委托价格（涨停价）
                    order_price = upper_limit
                elif abs(current_price - lower_limit) < 0.01:  # 跌停
                    status_desc = "跌停封板"
                    order_price = lower_limit
                elif current_price >= upper_limit * 0.98:  # 接近涨停
                    status_desc = "接近涨停"
                    order_price = upper_limit
                elif current_price <= lower_limit * 1.02:  # 接近跌停
                    status_desc = "接近跌停"
                    order_price = lower_limit
                else:
                    status_desc = f"{current_price}元"
                    # 根据价格类型和调整计算委托价格
                    try:
                        price_adjust_percent = float(self.price_adjust.get()) / 100
                    except:
                        price_adjust_percent = 0.5 / 100  # 默认0.5%
                    order_price = round(current_price * (1 + price_adjust_percent), 2)
                    # 确保不超过涨停价
                    if order_price > upper_limit:
                        order_price = upper_limit
            else:
                status_desc = f"{current_price}元"
                # 没有涨跌停信息时的默认处理
                try:
                    price_adjust_percent = float(self.price_adjust.get()) / 100
                except:
                    price_adjust_percent = 0.5 / 100  # 默认0.5%
                order_price = round(current_price * (1 + price_adjust_percent), 2)
            
            # 判断流动性状态
            if ask_price <= 0:  # 无卖盘
                if status_desc.endswith("涨停封板"):
                    liquidity_desc = "无卖盘"
                else:
                    liquidity_desc = "无卖盘"
            elif bid_price <= 0:  # 无买盘
                if status_desc.endswith("跌停封板"):
                    liquidity_desc = "无买盘"
                else:
                    liquidity_desc = "无买盘"
            else:
                liquidity_desc = "有卖盘"
            
            # 生成最终的状态信息
            if status_desc.endswith("元"):
                # 正常交易状态
                status_info = f"{base_code}({stock_name}): {status_desc} {liquidity_desc} 委托价{order_price}"
            else:
                # 涨停/跌停状态
                status_info = f"{base_code}({stock_name}): {current_price}元{status_desc} {liquidity_desc} 委托价{order_price}"
            
            return status_info
            
        except Exception:
            # 异常情况下返回基础信息
            base_code = stock_code.split('.')[0] if '.' in stock_code else stock_code
            return f"{base_code}({stock_name}): {current_price}元 状态未知 委托价{current_price}"

class MockEntry:
    """模拟输入框类"""
    def __init__(self, value):
        self.value = value
    
    def get(self):
        return self.value

def test_stock_status_format():
    """测试股票状态格式"""
    print("🧪 测试新的股票状态日志格式")
    print("=" * 60)
    
    trader = MockTrader()
    
    # 测试用例
    test_cases = [
        {
            "name": "涨停股票",
            "stock_code": "002901.SZ",
            "stock_name": "大博医疗",
            "current_price": 49.15,
            "upper_limit": 49.15,
            "lower_limit": 40.21,
            "ask_price": 0,
            "bid_price": 49.15,
            "expected": "002901(大博医疗): 49.15元涨停封板 无卖盘 委托价49.15"
        },
        {
            "name": "跌停股票",
            "stock_code": "000001.SZ",
            "stock_name": "平安银行",
            "current_price": 10.00,
            "upper_limit": 12.20,
            "lower_limit": 10.00,
            "ask_price": 10.01,
            "bid_price": 0,
            "expected": "000001(平安银行): 10.0元跌停封板 无买盘 委托价10.0"
        },
        {
            "name": "正常交易股票",
            "stock_code": "000002.SZ",
            "stock_name": "万科A",
            "current_price": 15.50,
            "upper_limit": 17.05,
            "lower_limit": 13.95,
            "ask_price": 15.51,
            "bid_price": 15.50,
            "expected": "000002(万科A): 15.5元 有卖盘 委托价15.58"
        },
        {
            "name": "接近涨停股票",
            "stock_code": "300001.SZ",
            "stock_name": "特锐德",
            "current_price": 19.80,
            "upper_limit": 20.00,
            "lower_limit": 16.36,
            "ask_price": 19.81,
            "bid_price": 19.80,
            "expected": "300001(特锐德): 19.8元接近涨停 有卖盘 委托价20.0"
        },
        {
            "name": "无涨跌停信息股票",
            "stock_code": "600000.SH",
            "stock_name": "浦发银行",
            "current_price": 8.50,
            "upper_limit": 0,
            "lower_limit": 0,
            "ask_price": 8.51,
            "bid_price": 8.50,
            "expected": "600000(浦发银行): 8.5元 有卖盘 委托价8.54"
        }
    ]
    
    # 执行测试
    for i, case in enumerate(test_cases, 1):
        print(f"\n📊 测试用例 {i}: {case['name']}")
        print("-" * 40)
        
        result = trader._generate_stock_status_info(
            case['stock_code'],
            case['stock_name'],
            case['current_price'],
            case['upper_limit'],
            case['lower_limit'],
            case['ask_price'],
            case['bid_price']
        )
        
        print(f"实际输出: {result}")
        print(f"预期输出: {case['expected']}")
        
        # 简单的匹配检查（允许小数点精度差异）
        if case['stock_code'].split('.')[0] in result and case['stock_name'] in result:
            print("✅ 格式正确")
        else:
            print("❌ 格式错误")

def test_real_stock_data():
    """测试真实股票数据"""
    print(f"\n{'='*60}")
    print("测试真实股票数据")
    print("=" * 60)
    
    trader = MockTrader()
    
    # 测试股票列表
    test_stocks = [
        "002901.SZ",  # 大博医疗
        "000001.SZ",  # 平安银行
        "600000.SH",  # 浦发银行
    ]
    
    for stock_code in test_stocks:
        print(f"\n📈 测试股票: {stock_code}")
        print("-" * 30)
        
        try:
            # 获取股票详情
            detail = xtdata.get_instrument_detail(stock_code)
            stock_name = detail.get('InstrumentName', '未知股票') if detail else '未知股票'
            upper_limit = detail.get('UpStopPrice', 0) if detail else 0
            lower_limit = detail.get('DownStopPrice', 0) if detail else 0
            
            # 获取行情数据
            market_data = xtdata.get_full_tick([stock_code])
            if market_data and stock_code in market_data:
                data = market_data[stock_code]
                current_price = data.get('lastPrice', 0)
                ask_price = data.get('askPrice', [0])[0] if data.get('askPrice') else 0
                bid_price = data.get('bidPrice', [0])[0] if data.get('bidPrice') else 0
                
                # 生成状态信息
                status_info = trader._generate_stock_status_info(
                    stock_code, stock_name, current_price, 
                    upper_limit, lower_limit, ask_price, bid_price
                )
                
                print(f"📊 {status_info}")
                
            else:
                print(f"❌ 无法获取 {stock_code} 的行情数据")
                
        except Exception as e:
            print(f"❌ 测试 {stock_code} 失败: {str(e)}")

def main():
    """主测试函数"""
    print("🔧 新日志格式测试")
    print("=" * 60)
    
    # 测试格式化函数
    test_stock_status_format()
    
    # 测试真实数据
    test_real_stock_data()
    
    print(f"\n{'='*60}")
    print("✅ 测试完成")
    print("新的日志格式将大幅简化输出，提高可读性")

if __name__ == "__main__":
    main()
