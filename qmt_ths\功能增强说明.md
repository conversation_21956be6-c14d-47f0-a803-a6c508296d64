# QMT与同花顺结合动态板块监控交易系统 - 功能增强说明

## 概述

本次功能增强为QMT与同花顺结合的动态板块监控交易系统添加了多项重要功能，提升了系统的灵活性、精确性和适用性。所有新功能都与现有系统完美集成，保持向后兼容性。

## 一、交易间隔精度升级

### 功能描述
- **原功能**：只支持整数秒的交易间隔（如1秒、2秒、30秒）
- **新功能**：支持小数秒精度的交易间隔设置

### 具体改进
- 支持0.1秒精度的交易间隔（如0.5秒、1.5秒、2.5秒等）
- 更新了所有相关的时间计算逻辑
- 添加了输入验证，确保间隔在0.1-3600秒范围内
- 默认值改为30.0秒，明确支持小数

### 使用示例
```
交易间隔设置：
- 0.5秒：高频交易
- 1.5秒：快速响应
- 30.0秒：标准间隔
- 60.5秒：慢速交易
```

## 二、多市场股票代码转换支持扩展

### 功能描述
在`read_block.py`的`market_map`字典中新增了大量市场代码映射，支持更多交易市场。

### 新增市场支持
1. **北交所股票**
   - 市场代码：81, 82, 83, 84
   - 后缀格式：.BJ
   - 支持8开头、4开头、9开头的股票代码

2. **新三板股票**
   - 市场代码：85, 86
   - 后缀格式：.NQ

3. **扩展美股支持**
   - 新增市场代码：72 (NYSE), 73 (AMEX)
   - 原有：70, 71, 87
   - 后缀格式：.US

4. **期货代码支持**
   - 市场代码：90 (商品期货), 91 (金融期货), 92 (期权), 93 (其他期货)
   - 后缀格式：.FT

5. **扩展港股支持**
   - 新增市场代码：77, 78
   - 原有：76, 79
   - 后缀格式：.HK

### 特殊处理
- **北交所无后缀支持**：自动识别8/4/9开头的6位数字代码
- **双格式输出**：北交所股票同时生成无后缀和.BJ后缀两种格式

## 三、股票过滤功能

### 功能描述
添加了可配置的股票过滤设置，允许用户自定义不买入的股票类型和不卖出的特定股票。

### GUI界面新增
- **股票过滤设置区域**：新增独立的设置面板
- **不买入股票类型勾选框**：
  - 北交所
  - 创业板
  - 科创板
  - 新三板
- **不卖出股票输入框**：支持输入特定股票代码列表

### 配置保存
- 所有过滤设置自动保存到`trader_config.json`配置文件
- 下次启动时自动加载之前的设置
- 无需重复配置

### 过滤逻辑
1. **买入过滤**：
   - 检查股票是否属于被过滤的类型
   - 被过滤的股票不会加入买入列表
   - 记录过滤日志

2. **卖出过滤**：
   - 检查股票是否在不卖出列表中
   - 不卖出列表中的股票跳过卖出操作
   - 支持多种分隔符（逗号、分号、空格）

### 使用示例
```
不买入设置：
北交所  创业板  科创板  新三板

不卖出股票：
000001.SZ,600000.SH,300001.SZ
```

## 四、交易所格式支持扩展

### 新增支持格式
- **科创板格式**：18开头的股票自动添加.SH后缀
- **北交所格式**：81/82/83/84开头的股票自动添加.BJ后缀
- **新三板格式**：85/86开头的股票自动添加.NQ后缀

### 代码优化
- 优化了市场代码映射逻辑
- 增强了代码可读性和可维护性
- 统一了格式处理方式

## 五、技术实现细节

### 核心文件修改

1. **tonghuashun_gui.py**
   - 新增股票过滤设置界面
   - 实现过滤逻辑
   - 添加配置加载和保存

2. **read_block.py**
   - 扩展市场代码映射
   - 优化股票代码转换逻辑
   - 增强代码健壮性

### 代码质量保证

**向后兼容性**：
- 所有修改都保持向后兼容
- 现有配置文件可以正常加载
- 不影响其他功能的正常运行

**错误处理**：
- 增加了输入验证
- 完善了异常处理机制
- 提供了友好的错误提示

## 六、使用说明

### 如何配置股票过滤

1. **不买入设置**：
   - 在GUI界面勾选相应选项
   - 系统将自动过滤指定类型的股票

2. **不卖出设置**：
   - 在文本框中输入不卖出的股票代码
   - 支持多种分隔符

### 配置持久化

- 所有设置自动保存到配置文件
- 重启后自动加载
- 支持手动编辑配置文件

## 七、测试验证

### 功能测试
- 验证所有新增功能正常工作
- 确认过滤逻辑正确执行
- 测试配置持久化功能

### 兼容性测试
- 验证与现有功能的兼容性
- 确认配置文件向后兼容
- 测试不同操作系统环境

## 八、总结

本次功能增强显著提升了系统的灵活性和适用性，通过扩展市场支持、增加过滤功能和完善配置管理，使系统能够更好地适应不同用户的需求。所有修改都遵循了良好的编程实践，确保了代码质量和系统稳定性。
